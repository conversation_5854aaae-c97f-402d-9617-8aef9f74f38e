@layer legacy {
    .floatButton {
        --apl-float-button-min-width: 42px;
        --apl-float-button-min-height: 42px;
        --apl-float-button-border-radius: 56px;
        --apl-float-button-base-icon-size: 16px;
    }

    .expanded {
        --apl-float-button-padding: var(--apl-space-padding-xs, 8px) var(--apl-space-padding-md, 16px);
        --apl-float-button-width: fit-content;
        --apl-float-button-label-gap: var(--space-gap-xs, 8px);
        --apl-float-button-label-opacity: 1;
    }

    .collapsed {
        --apl-float-button-padding: var(--apl-space-padding-xs, 8px) 12px;
        --apl-float-button-width: 16px;
        --apl-float-button-label-gap: var(--space-gap-2xs, 4px);
        --apl-float-button-label-opacity: 0;
        --apl-float-button-icon-alignment: center;
    }
}

@layer apollo {
    .floatButton {
        --apl-float-button-min-width: 48px;
        --apl-float-button-min-height: 48px;
        --apl-float-button-border-radius: var(--apl-alias-radius-radius11, 9999px);
        --apl-float-button-base-icon-size: 20px;
    }

    .expanded {
        --apl-float-button-padding: var(--apl-alias-spacing-padding-padding4, 6px) var(----apl-alias-spacing-padding-padding8, 16px);
        --apl-float-button-width: fit-content;
        --apl-float-button-label-gap: var(--apl-alias-spacing-gap-gap4, 6px);
        --apl-float-button-label-opacity: 1;
    }

    .collapsed {
        --apl-float-button-padding: var(--apl-alias-spacing-padding-padding4, 6px) var(----apl-alias-spacing-padding-padding8, 13px);
        --apl-float-button-width: 48px;
        --apl-float-button-label-gap: var(--apl-alias-spacing-gap-gap4, 6px);
        --apl-float-button-label-opacity: 0;
        --apl-float-button-icon-alignment: center;
    }
}

.root {
    padding: var(--apl-float-button-padding);
    min-width: var(--apl-float-button-min-width);
    min-height: var(--apl-float-button-min-height);
    border-radius: var(--apl-float-button-border-radius);
    overflow: hidden;
    transition: all 240ms cubic-bezier(0.4, 0, 0.2, 1);
    width: var(--apl-float-button-width);
}

.iconStart {
    justify-content: start;
}

.iconEnd {
    justify-content: end;
}

.icon {
    width: var(--apl-float-button-base-icon-size);
    height: var(--apl-float-button-base-icon-size);
    color: inherit;

    & svg {
        width: var(--apl-float-button-base-icon-size);
        height: var(--apl-float-button-base-icon-size);
    }
}

.label {
    transition: opacity;
    gap: var(--apl-float-button-label-gap, 8px);
    opacity: var(--apl-float-button-label-opacity);

}